# FlashCpp PDB Debugging Analysis Plan

## Current Status
- ✅ All PDB files have similar entry counts (~13,000)
- ✅ All executables link and run successfully
- ❌ Function ID assignments differ between compilers

## Identified Issues

### 1. Function ID Mismatch
| Compiler | add() | main() |
|----------|-------|--------|
| FlashCpp | 0x1005 | 0x1006 |
| Clang    | 0x1000 | 0x1001 |
| MSVC     | 0x1000 | 0x1003 |

### 2. Type Reference Issues
- FlashCpp: Type=0x1001, Type=0x1003
- Clang: Type=0x1001, Type=0x1003  
- MSVC: Type=0x1002, Type=0x1006

## Root Cause Analysis

### Issue: Function ID Assignment Order
FlashCpp assigns function IDs starting from 0x1005/0x1006, while Clang starts from 0x1000/0x1001.

**Hypothesis**: Flash<PERSON><PERSON> is generating build info records (0x1000-0x1004) before function records, while Clang generates function records first.

### Issue: Type Index References
Different type index assignments suggest the order of type generation differs between compilers.

## Testing Plan

### Phase 1: Verify Symbol Loading
1. Test with Visual Studio debugger
2. Test with WinDbg
3. Check if symbols load correctly
4. Verify breakpoint functionality

### Phase 2: Source Line Mapping
1. Test if debugger can map to source lines
2. Check if variable inspection works
3. Verify call stack display

### Phase 3: Fix Function ID Assignment
1. Modify FlashCpp to generate function IDs first (0x1000, 0x1001)
2. Move build info generation after function generation
3. Test if this improves debugger compatibility

## Expected Fixes

### 1. Reorder Type Generation in CodeViewDebug.cpp
```cpp
// Current order:
// 1. Build info (0x1000-0x1004)
// 2. Functions (0x1005-0x1006)

// Should be:
// 1. Functions (0x1000-0x1001) 
// 2. Build info (0x1002-0x1006)
```

### 2. Match Clang's Type Assignment Pattern
- Ensure function types are assigned consistently
- Match the LF_PROCEDURE and LF_FUNC_ID pattern from Clang

## Success Criteria
- Function IDs match Clang pattern (0x1000, 0x1001)
- Debugger can set breakpoints on add() and main()
- Variable inspection works correctly
- Source line mapping functions properly
