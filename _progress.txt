C++20 COMPILER DEBUGGING WORKFLOW - FLASHCPP PROJECT
===================================================

⚠️ CRITICAL REMINDER: ALWAYS RECOMPILE FLASHCPP BEFORE TESTING! ⚠️
================================================================
MUST run build command after ANY changes to src/CodeViewDebug.cpp:
- powershell -Command "(Get-Item src\CodeViewDebug.cpp).LastWriteTime = Get-Date"
- .\build_flashcpp.bat
Changes will NOT take effect until FlashCpp.exe is rebuilt!

CURRENT CRITICAL ISSUE - FILE ID AND STRING TABLE:
===============================================
cvdump output shows:
(00000C) S_OBJNAME: Signature: 00000000, C:\Projects\FlashCpp\test_debug.obj
(000038) S_GPROC32_ID: [0003:00000000], Cb: 0000001A, ID: 0x1000, add
(000063) S_FRAMEPROC: Frame size = 0x00000000 bytes
(000081) S_LOCAL: Param: 00000074, a
(00008D) S_DEFRANGE_FRAMEPOINTER_REL: FrameOffset: 0008
(00009D) S_LOCAL: Param: 00000074, b
(0000A9) S_DEFRANGE_FRAMEPOINTER_REL: FrameOffset: 0010
(0000B9) S_PROC_ID_END
(0000BD) S_GPROC32_ID: [0003:00000020], Cb: 00000026, ID: 0x1000, main
(0000E9) S_FRAMEPROC: Frame size = 0x00000000 bytes
(000107) S_PROC_ID_END

*** FILECHKSUMS
FileId  St.Offset  Cb  Type  ChksumBytes
     0  00000001   20  SHA_256  AF735EC9D792914D046BF7FC2B16B2372BC65E7F7E783F8DA319050FA1FA1F4D

*** LINES
  0003:00000000-0000001A, flags = 0000, fileid = 00000000
      3 00000013
  fileid = 000000F2
CVDUMP : fatal error : Incorrect line block header

ANALYSIS:
========
✅ S_OBJNAME structure is CORRECT (signature and object name)
✅ S_GPROC32_ID structures are CORRECT (names, offsets, types all perfect)
✅ S_FRAMEPROC structures are CORRECT (frame size and flags)
✅ S_LOCAL and S_DEFRANGE_FRAMEPOINTER_REL are CORRECT
✅ FILECHKSUMS section is CORRECT (SHA-256 hash and file ID)
❌ CRITICAL: Line information has incorrect file ID (0xF2 = 242)
❌ CRITICAL: String table implementation needs fixing

PROGRESS UPDATE:
===============
✅ FIXED: Function ID assignment - unique IDs per function (0x1000, 0x1001)
✅ FIXED: String table generation - working correctly with proper offsets
✅ FIXED: Symbol records - all parsing perfectly
✅ FIXED: Function length calculation - functions added with correct lengths
✅ FIXED: Line information structure - separate subsections per function
✅ FIXED: LineInfoHeader size (12 bytes)
✅ FIXED: First function line information parsing

CURRENT STATUS:
==============
- Function IDs: ✅ Working (add=0x1002, main=0x1005) - LF_FUNC_ID indices
- String table: ✅ Working (37 bytes, contains source file path)
- Type information: ✅ Complete dynamic generation (96 bytes .debug$T)
  * LF_ARGLIST records for each function's parameters
  * LF_PROCEDURE records for each function signature
  * LF_FUNC_ID records linking names to types
- Line information: ✅ COMPLETELY FIXED (96 bytes line data, 3 lines per function)
  * Fixed "Incorrect line block header" error (block_size now 36 bytes)
  * Correct file IDs (0 for both functions)
  * Proper LineNumberEntry bit field packing
  * No more invalid file IDs (0x20, 0xF3)
- Debug sections: ✅ Generated (468 bytes .debug$S, 96 bytes .debug$T)
- Section numbers: ✅ Fixed to use section 0 (matches MSVC/clang reference)
- Function flags: ✅ Added flags (need correct values for "Do Not Inline, Optimized Debug Info")
- File writing: ❌ COFFI save failing, manual fallback lacks debug sections

ROOT CAUSE:
==========
File writing issue:
- COFFI save is failing for unknown reason
- Manual fallback only creates basic .text section, no debug sections
- All debug information generation is working perfectly
- Need to either fix COFFI save or enhance manual fallback with debug sections

MAJOR ACHIEVEMENTS:
==================
✅ Dynamic type generation - completely removes hardcoded function names
✅ Line information generation - COMPLETELY FIXED with correct cvdump structure
✅ Section number fixes - now uses section 0 to match MSVC/clang reference
✅ Function ID consistency - add=0x1002, main=0x1005 (LF_FUNC_ID indices)
✅ Complete debug information pipeline - all components working
✅ Debug section generation - 456 bytes .debug$S + 96 bytes .debug$T
✅ All subsections working - symbols, line info, string table, file checksums
✅ Line information structure - matches cvdump parsing logic exactly
✅ Main header generation - offCon=0, segCon=0, cbCon=58 (correct format)
✅ File block structure - proper file_id=0, block_size=36 for each function

CURRENT ISSUE - PDB DEBUGGING ANALYSIS:
======================================
✅ CVDUMP ANALYSIS COMPLETED: Debug information generation is working correctly
   - All major symbols present: S_OBJNAME, S_COMPILE3, S_BUILDINFO, S_GPROC32_ID
   - Function IDs correct: add=0x1002, main=0x1005 (LF_FUNC_ID indices)
   - Type information complete: LF_ARGLIST, LF_PROCEDURE, LF_FUNC_ID, LF_BUILDINFO
   - Line information working: Correct file IDs and line mappings
   - Parameter information: S_LOCAL and S_DEFRANGE_FRAMEPOINTER_REL records

❌ KEY DIFFERENCES FROM MSVC REFERENCE:
   - Section numbering: FlashCpp uses sections #1/#2, MSVC uses #2/#6
   - Parameter format: FlashCpp uses S_LOCAL/S_DEFRANGE_FRAMEPOINTER_REL, MSVC uses S_REGREL32
   - Missing S_UNAMESPACE symbols (__vc_attributes, helper_attributes, atl, std)
   - Missing LF_TYPESERVER2 record (MSVC references vc140.pdb)
   - Function flags: FlashCpp has "Do Not Inline, Optimized Debug Info", MSVC has different flags

NEW DEBUGGING STRATEGY:
======================
✅ PLAN: Use systematic section removal from working test_debug_clang_ref.obj
   - Start with working clang reference object
   - Remove sections one by one and test linking
   - Identify minimum required components for successful linking
   - Compare with our implementation to find missing pieces

✅ ANALYSIS COMPLETED: Section comparison revealed key differences:
   - Our .debug$T section: 93 bytes vs Clang's 3600 bytes (MAJOR DIFFERENCE!)
   - Missing .xdata and .pdata sections (exception handling)
   - Different text section name (.text$mn vs .text)

✅ IMPLEMENTED: Added missing build information records:
   - LF_STRING_ID records for directory, compiler, source file, command line
   - LF_BUILDINFO record referencing the string IDs
   - S_BUILDINFO symbol referencing the build info record
   - Fixed LF_FUNC_ID to reference procedure types instead of T_NOTYPE

❌ CURRENT STATUS: Still failing to link - debug information corruption error persists
   - Build information added but linking still fails
   - May need to investigate structure format or alignment issues

✅ DISCOVERED: Type index writing bug confirmed!
   - cvdump shows LF_FUNC_ID records still reference T_NOTYPE(0000) instead of LF_PROCEDURE
   - LF_PROCEDURE for main shows "Arg list type = 0x1001" but should be "0x1003"
   - Type generation order: LF_ARGLIST (0x1000), LF_PROCEDURE (0x1001), LF_FUNC_ID (0x1002)
   - Both LF_FUNC_ID and LF_PROCEDURE type references are being written incorrectly
   - Issue is in the binary data writing, not the index calculation logic
   - Need to investigate how type indices are being serialized to binary format

NEXT STEPS:
===========
1. Test S_COMPILE3 implementation:
   - Rebuild FlashCpp.exe
   - Generate test_debug.obj
   - Check with cvdump for S_COMPILE3 presence
   - Test linking to see if corruption error is resolved

2. Fix file writing to include debug sections:
   - Either fix COFFI save issue or enhance manual fallback
   - Include .debug$S and .debug$T sections in final file
   - Test with cvdump to verify section 0 and function flags work correctly

3. Remove temporary line mappings and implement proper line number tracking:
   - Connect parser line numbers to IR instructions
   - Remove hardcoded test line mappings
   - Implement dynamic line number detection from source

BUILD AND TEST:
==============
1. Build the project:
   ```powershell
   .\build_flashcpp.bat
   ```

2. Run test case:
   ```powershell
   .\FlashCpp.exe test_debug.cpp
   ```

3. Verify debug info:
   ```powershell
   cvdump.exe test_debug.obj
   ```

4. Check for:
   - Correct file IDs in line information
   - Valid string table entries
   - No alignment issues
   - No buffer overruns


